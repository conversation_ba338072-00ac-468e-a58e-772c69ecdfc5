# Red Text Plugin для Exteragram

Плагин, который делает весь текст в Telegram красным цветом.

## Описание

Red Text Plugin использует Xposed хуки для перехвата методов `TextView.setText()` и изменения цвета текста на красный на системном уровне. Это означает, что весь текст в приложении Telegram будет отображаться красным цветом.

## Функции

- ✅ **Красный цвет для всего текста** - весь текст в приложении становится красным
- ✅ **Настройки включения/выключения** - можно легко включить или выключить функцию
- ✅ **Префикс для исходящих сообщений** - опционально добавляет красный кружок 🔴 к вашим сообщениям
- ✅ **Перезапуск хуков** - возможность перезапустить хуки без перезагрузки плагина
- ✅ **Информационные уведомления** - показывает статус работы плагина

## Установка

1. Убедитесь, что у вас установлен Exteragram
2. Убедитесь, что настроена среда разработки для плагинов Exteragram
3. Скопируйте файл `red_text_plugin.py` в папку с плагинами
4. Запустите плагин командой:
   ```bash
   extera red_text_plugin.py
   ```

## Использование

### Основные настройки

1. **Включить красный текст** - основная функция плагина
   - Включает/выключает изменение цвета текста на красный
   - По умолчанию включено

2. **Добавлять префикс к исходящим сообщениям** 
   - Добавляет красный кружок 🔴 перед вашими сообщениями
   - По умолчанию выключено

### Дополнительные функции

- **Информация о плагине** - показывает подробную информацию о плагине
- **Перезапустить хуки** - перезапускает Xposed хуки без перезагрузки плагина

## Техническая информация

### Как это работает

Плагин использует следующие технологии:

1. **Xposed Framework** - для перехвата методов Android
2. **TextView Hooks** - перехватывает вызовы `setText()` в TextView
3. **Color Modification** - изменяет цвет текста на красный (0xFFFF0000)

### Перехватываемые методы

- `TextView.setText(CharSequence)`
- `TextView.setText(CharSequence, BufferType)`

### Хуки сообщений

- `on_send_message_hook` - перехватывает исходящие сообщения
- `on_update_hook` - перехватывает входящие обновления

## Настройки плагина

Плагин сохраняет следующие настройки:

- `enabled` (bool) - включен ли плагин (по умолчанию: true)
- `add_prefix` (bool) - добавлять ли префикс к сообщениям (по умолчанию: false)

## Устранение неполадок

### Плагин не работает

1. Убедитесь, что Exteragram поддерживает Xposed хуки
2. Проверьте логи плагина на наличие ошибок
3. Попробуйте перезапустить хуки через настройки плагина

### Не весь текст красный

1. Некоторые элементы UI могут использовать другие способы отображения текста
2. Попробуйте перезапустить приложение после включения плагина

### Ошибки в логах

Если вы видите ошибки типа "Failed to hook TextView", это может означать:
- Изменения в структуре Android API
- Проблемы с правами доступа
- Конфликты с другими плагинами

## Разработка

### Структура кода

```python
class RedTextPlugin(BasePlugin):
    def on_plugin_load(self):      # Инициализация плагина
    def _setup_textview_hooks(self): # Настройка Xposed хуков
    def on_send_message_hook(self):  # Перехват исходящих сообщений
    def on_update_hook(self):        # Перехват входящих сообщений
    def create_settings(self):       # Интерфейс настроек
```

### Добавление новых функций

Для добавления новых функций можно:
1. Добавить новые настройки в `create_settings()`
2. Расширить логику в хуках
3. Добавить новые Xposed хуки для других UI элементов

## Лицензия

Этот плагин создан для демонстрационных целей. Используйте на свой страх и риск.

## Автор

Создано с помощью Augment Agent для демонстрации возможностей плагинов Exteragram.

---

**Примечание**: Этот плагин изменяет отображение текста на системном уровне. Будьте осторожны при использовании в продакшене.
