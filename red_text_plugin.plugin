"""
Red Text Plugin for Exteragram
Делает весь текст в приложении красным
"""

from typing import Any
from android_utils import log
from base_plugin import BasePlugin, <PERSON>H<PERSON>, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from hook_utils import find_class
from ui.settings import Header, Switch, Divider, Text
from ui.bulletin import BulletinHelper

# Метаданные плагина
__id__ = "red_text_plugin"
__name__ = "Red Text Plugin"
__description__ = "Делает весь текст в Telegram красным цветом"
__author__ = "@your_username"
__version__ = "1.0.0"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"


class TextViewHook(MethodHook):
    """Хук для изменения цвета текста в TextView"""
    
    def __init__(self, plugin_instance):
        super().__init__()
        self.plugin = plugin_instance
    
    def after_hooked_method(self, param):
        """Вызывается после установки текста в TextView"""
        try:
            # Проверяем, включен ли плагин
            if not self.plugin.get_setting("enabled", True):
                return
                
            # Получаем TextView объект
            text_view = param.thisObject
            
            # Устанавливаем красный цвет текста
            # Используем Android Color.RED (0xFFFF0000)
            text_view.setTextColor(0xFFFF0000)
            
        except Exception as e:
            self.plugin.log(f"Error in TextViewHook: {str(e)}")


class RedTextPlugin(BasePlugin):
    """Основной класс плагина для красного текста"""
    
    def __init__(self):
        super().__init__()
        self.hooked_methods = []
    
    def on_plugin_load(self):
        """Вызывается при загрузке плагина"""
        self.log("Red Text Plugin загружается...")
        
        # Регистрируем хуки для перехвата сообщений
        self.add_on_send_message_hook()
        self.add_on_update_hook()
        
        # Устанавливаем Xposed хуки для TextView
        self._setup_textview_hooks()
        
        self.log("Red Text Plugin успешно загружен!")
        
        # Показываем уведомление о загрузке
        BulletinHelper.show_simple("Red Text Plugin активирован!", 0x7f0e0001)
    
    def on_plugin_unload(self):
        """Вызывается при выгрузке плагина"""
        self.log("Red Text Plugin выгружается...")
        
        # Очищаем хуки
        self.hooked_methods.clear()
        
        self.log("Red Text Plugin выгружен!")
    
    def _setup_textview_hooks(self):
        """Настраивает Xposed хуки для TextView"""
        try:
            # Находим класс TextView
            TextView = find_class("android.widget.TextView")
            CharSequence = find_class("java.lang.CharSequence")
            
            # Хукаем метод setText(CharSequence)
            set_text_method = TextView.getDeclaredMethod("setText", CharSequence)
            text_hook = TextViewHook(self)
            self.hook_method(set_text_method, text_hook)
            self.hooked_methods.append(set_text_method)
            
            self.log("TextView.setText() успешно захукан")
            
            # Также хукаем метод setText(CharSequence, BufferType)
            try:
                BufferType = find_class("android.widget.TextView$BufferType")
                set_text_buffer_method = TextView.getDeclaredMethod("setText", CharSequence, BufferType)
                self.hook_method(set_text_buffer_method, text_hook)
                self.hooked_methods.append(set_text_buffer_method)
                self.log("TextView.setText(CharSequence, BufferType) успешно захукан")
            except Exception as e:
                self.log(f"Не удалось захукать setText с BufferType: {str(e)}")
                
        except Exception as e:
            self.log(f"Ошибка при настройке TextView хуков: {str(e)}")
    
    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Перехватывает исходящие сообщения"""
        result = HookResult()
        
        # Проверяем, включен ли плагин
        if not self.get_setting("enabled", True):
            return result
        
        if hasattr(params, 'message') and isinstance(params.message, str):
            self.log(f"Перехвачено исходящее сообщение: {params.message[:50]}...")
            
            # Здесь можно добавить дополнительную логику для исходящих сообщений
            # Например, добавить префикс или суффикс
            if self.get_setting("add_prefix", False):
                params.message = f"🔴 {params.message}"
                result.strategy = HookStrategy.MODIFY
                result.params = params
        
        return result
    
    def on_update_hook(self, update_name: str, account: int, update: Any) -> HookResult:
        """Перехватывает входящие обновления"""
        result = HookResult()
        
        # Проверяем, включен ли плагин
        if not self.get_setting("enabled", True):
            return result
        
        if update_name == "TL_updateNewMessage":
            self.log(f"Перехвачено входящее сообщение: {update_name}")
            
            # Здесь можно добавить дополнительную логику для входящих сообщений
            # Основная работа по изменению цвета происходит в TextView хуках
        
        return result
    
    def create_settings(self):
        """Создает интерфейс настроек плагина"""
        return [
            Header(text="Настройки Red Text Plugin"),
            
            Switch(
                key="enabled",
                text="Включить красный текст",
                default=True,
                subtext="Делает весь текст в приложении красным",
                icon="msg_palette",
                on_change=self._on_enabled_change
            ),
            
            Switch(
                key="add_prefix",
                text="Добавлять префикс к исходящим сообщениям",
                default=False,
                subtext="Добавляет красный кружок 🔴 к вашим сообщениям",
                icon="msg_edit",
                on_change=self._on_prefix_change
            ),
            
            Divider(text="Плагин изменяет цвет текста на системном уровне"),
            
            Text(
                text="Информация о плагине",
                icon="msg_info",
                on_click=self._show_info
            ),
            
            Text(
                text="Перезапустить хуки",
                icon="msg_retry",
                red=True,
                on_click=self._restart_hooks
            )
        ]
    
    def _on_enabled_change(self, new_value: bool):
        """Обработчик изменения состояния включения плагина"""
        self.log(f"Плагин {'включен' if new_value else 'выключен'}")
        
        if new_value:
            BulletinHelper.show_simple("Red Text Plugin включен!", 0x7f0e0001)
        else:
            BulletinHelper.show_simple("Red Text Plugin выключен", 0x7f0e0002)
    
    def _on_prefix_change(self, new_value: bool):
        """Обработчик изменения настройки префикса"""
        self.log(f"Префикс для сообщений {'включен' if new_value else 'выключен'}")
    
    def _show_info(self, view):
        """Показывает информацию о плагине"""
        info_text = (
            "Red Text Plugin v1.0.0\n\n"
            "Этот плагин делает весь текст в Telegram красным цветом, "
            "используя Xposed хуки для перехвата методов TextView.\n\n"
            "Функции:\n"
            "• Красный цвет для всего текста\n"
            "• Опциональный префикс для исходящих сообщений\n"
            "• Настройки включения/выключения"
        )
        
        BulletinHelper.show_simple(info_text, 0x7f0e0001)
    
    def _restart_hooks(self, view):
        """Перезапускает хуки TextView"""
        try:
            self.log("Перезапуск хуков...")
            self._setup_textview_hooks()
            BulletinHelper.show_simple("Хуки перезапущены!", 0x7f0e0001)
        except Exception as e:
            self.log(f"Ошибка при перезапуске хуков: {str(e)}")
            BulletinHelper.show_simple(f"Ошибка: {str(e)}", 0x7f0e0002)
