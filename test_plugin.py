#!/usr/bin/env python3
"""
Тестовый скрипт для проверки Red Text Plugin
"""

import sys
import os

def test_plugin_structure():
    """Проверяет структуру плагина"""
    print("🔍 Проверка структуры плагина...")
    
    # Проверяем наличие файла плагина
    if not os.path.exists("red_text_plugin.py"):
        print("❌ Файл red_text_plugin.py не найден!")
        return False
    
    print("✅ Файл плагина найден")
    
    # Проверяем содержимое файла
    try:
        with open("red_text_plugin.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Проверяем наличие обязательных метаданных
        required_metadata = [
            "__id__", "__name__", "__description__", 
            "__author__", "__version__", "__icon__", "__min_version__"
        ]
        
        for metadata in required_metadata:
            if metadata not in content:
                print(f"❌ Отсутствует метаданные: {metadata}")
                return False
        
        print("✅ Все метаданные присутствуют")
        
        # Проверяем наличие основного класса
        if "class RedTextPlugin(BasePlugin):" not in content:
            print("❌ Основной класс плагина не найден!")
            return False
        
        print("✅ Основной класс плагина найден")
        
        # Проверяем наличие основных методов
        required_methods = [
            "on_plugin_load", "on_plugin_unload", 
            "create_settings", "_setup_textview_hooks"
        ]
        
        for method in required_methods:
            if f"def {method}" not in content:
                print(f"❌ Отсутствует метод: {method}")
                return False
        
        print("✅ Все основные методы присутствуют")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при чтении файла: {str(e)}")
        return False

def test_imports():
    """Проверяет импорты плагина"""
    print("\n🔍 Проверка импортов...")
    
    try:
        # Симулируем проверку импортов
        required_imports = [
            "from android_utils import log",
            "from base_plugin import BasePlugin",
            "from hook_utils import find_class",
            "from ui.settings import",
            "from ui.bulletin import BulletinHelper"
        ]
        
        with open("red_text_plugin.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        for import_line in required_imports:
            if import_line not in content:
                print(f"❌ Отсутствует импорт: {import_line}")
                return False
        
        print("✅ Все необходимые импорты присутствуют")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при проверке импортов: {str(e)}")
        return False

def show_plugin_info():
    """Показывает информацию о плагине"""
    print("\n📋 Информация о плагине:")
    print("=" * 50)
    
    try:
        with open("red_text_plugin.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Извлекаем метаданные
        metadata = {}
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('__') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"\'')
                metadata[key] = value
        
        for key, value in metadata.items():
            print(f"{key}: {value}")
        
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Ошибка при извлечении информации: {str(e)}")

def show_usage_instructions():
    """Показывает инструкции по использованию"""
    print("\n📖 Инструкции по использованию:")
    print("=" * 50)
    print("1. Убедитесь, что у вас установлен Exteragram")
    print("2. Настройте среду разработки для плагинов")
    print("3. Запустите плагин командой:")
    print("   extera red_text_plugin.py")
    print("4. Или для отладки:")
    print("   extera red_text_plugin.py --debug")
    print("=" * 50)
    print("\n🎯 Функции плагина:")
    print("• Делает весь текст в Telegram красным")
    print("• Настройки включения/выключения")
    print("• Опциональный префикс для сообщений")
    print("• Перезапуск хуков без перезагрузки")

def main():
    """Основная функция тестирования"""
    print("🚀 Тестирование Red Text Plugin для Exteragram")
    print("=" * 60)
    
    # Проверяем структуру плагина
    if not test_plugin_structure():
        print("\n❌ Тесты структуры плагина провалены!")
        sys.exit(1)
    
    # Проверяем импорты
    if not test_imports():
        print("\n❌ Тесты импортов провалены!")
        sys.exit(1)
    
    print("\n✅ Все тесты пройдены успешно!")
    
    # Показываем информацию о плагине
    show_plugin_info()
    
    # Показываем инструкции
    show_usage_instructions()
    
    print("\n🎉 Плагин готов к использованию!")

if __name__ == "__main__":
    main()
