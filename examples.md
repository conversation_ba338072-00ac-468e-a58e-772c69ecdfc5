# Примеры использования Red Text Plugin

## Основные сценарии использования

### 1. Базовое использование

После установки и запуска плагина:

```bash
# Запуск плагина
extera red_text_plugin.py

# Запуск с отладкой
extera red_text_plugin.py --debug
```

Весь текст в Telegram автоматически станет красным.

### 2. Настройка через интерфейс

1. Откройте настройки плагина в Exteragram
2. Найдите секцию "Red Text Plugin"
3. Доступные настройки:
   - **Включить красный текст** - основная функция
   - **Добавлять префикс к исходящим сообщениям** - добавляет 🔴

### 3. Программное управление настройками

```python
# В коде плагина можно управлять настройками:

# Включить плагин
self.set_setting("enabled", True)

# Выключить плагин
self.set_setting("enabled", False)

# Включить префикс для сообщений
self.set_setting("add_prefix", True)

# Получить текущее состояние
is_enabled = self.get_setting("enabled", True)
```

## Примеры кода для расширения функциональности

### 1. Добавление новых цветов

```python
class ColorTextHook(MethodHook):
    def __init__(self, plugin_instance):
        super().__init__()
        self.plugin = plugin_instance
    
    def after_hooked_method(self, param):
        try:
            if not self.plugin.get_setting("enabled", True):
                return
                
            text_view = param.thisObject
            
            # Выбор цвета в зависимости от настроек
            color_mode = self.plugin.get_setting("color_mode", "red")
            
            if color_mode == "red":
                text_view.setTextColor(0xFFFF0000)  # Красный
            elif color_mode == "blue":
                text_view.setTextColor(0xFF0000FF)  # Синий
            elif color_mode == "green":
                text_view.setTextColor(0xFF00FF00)  # Зеленый
                
        except Exception as e:
            self.plugin.log(f"Error in ColorTextHook: {str(e)}")
```

### 2. Добавление настройки выбора цвета

```python
def create_settings(self):
    return [
        Header(text="Настройки цвета текста"),
        
        Switch(
            key="enabled",
            text="Включить изменение цвета",
            default=True,
            icon="msg_palette"
        ),
        
        Selector(
            key="color_mode",
            text="Выбор цвета",
            default=0,
            items=["Красный", "Синий", "Зеленый", "Фиолетовый"],
            icon="msg_color",
            on_change=self._on_color_change
        )
    ]

def _on_color_change(self, new_index: int):
    colors = ["red", "blue", "green", "purple"]
    color_name = colors[new_index]
    self.log(f"Выбран цвет: {color_name}")
```

### 3. Фильтрация по типу сообщений

```python
def on_update_hook(self, update_name: str, account: int, update: Any) -> HookResult:
    result = HookResult()
    
    if not self.get_setting("enabled", True):
        return result
    
    if update_name == "TL_updateNewMessage":
        # Проверяем тип сообщения
        if hasattr(update, 'message'):
            message = update.message
            
            # Применяем красный цвет только к текстовым сообщениям
            if hasattr(message, 'message') and isinstance(message.message, str):
                # Можно добавить дополнительную логику
                if self.get_setting("only_text_messages", True):
                    self.log("Обрабатываем только текстовые сообщения")
    
    return result
```

## Расширенные примеры

### 1. Плагин с градиентным текстом

```python
class GradientTextHook(MethodHook):
    def __init__(self, plugin_instance):
        super().__init__()
        self.plugin = plugin_instance
        self.color_index = 0
        self.colors = [0xFFFF0000, 0xFFFF4500, 0xFFFF8C00, 0xFFFFA500]
    
    def after_hooked_method(self, param):
        try:
            if not self.plugin.get_setting("gradient_enabled", False):
                return
                
            text_view = param.thisObject
            
            # Циклически меняем цвета
            color = self.colors[self.color_index % len(self.colors)]
            text_view.setTextColor(color)
            self.color_index += 1
            
        except Exception as e:
            self.plugin.log(f"Error in GradientTextHook: {str(e)}")
```

### 2. Условное применение цвета

```python
def on_send_message_hook(self, account: int, params: Any) -> HookResult:
    result = HookResult()
    
    if not self.get_setting("enabled", True):
        return result
    
    if hasattr(params, 'message') and isinstance(params.message, str):
        message = params.message
        
        # Применяем красный цвет только к сообщениям с определенными словами
        trigger_words = self.get_setting("trigger_words", "важно,срочно,внимание").split(",")
        
        should_apply = any(word.strip().lower() in message.lower() for word in trigger_words)
        
        if should_apply:
            # Добавляем специальный маркер для обработки в TextView хуке
            params.message = f"[RED]{message}[/RED]"
            result.strategy = HookStrategy.MODIFY
            result.params = params
    
    return result
```

### 3. Интеграция с другими плагинами

```python
def on_plugin_load(self):
    # Проверяем наличие других плагинов
    try:
        # Пример интеграции с гипотетическим плагином тем
        theme_plugin = self.get_plugin_by_id("theme_plugin")
        if theme_plugin:
            # Получаем цвет темы
            theme_color = theme_plugin.get_setting("accent_color", 0xFFFF0000)
            self.set_setting("text_color", theme_color)
            self.log("Интеграция с плагином тем активирована")
    except Exception as e:
        self.log(f"Ошибка интеграции: {str(e)}")
    
    # Стандартная инициализация
    self.add_on_send_message_hook()
    self._setup_textview_hooks()
```

## Отладка и диагностика

### 1. Включение подробного логирования

```python
def _setup_textview_hooks(self):
    try:
        TextView = find_class("android.widget.TextView")
        CharSequence = find_class("java.lang.CharSequence")
        
        # Включаем подробное логирование
        if self.get_setting("debug_mode", False):
            self.log("Режим отладки включен")
        
        set_text_method = TextView.getDeclaredMethod("setText", CharSequence)
        text_hook = TextViewHook(self)
        self.hook_method(set_text_method, text_hook)
        
        if self.get_setting("debug_mode", False):
            self.log(f"Захукан метод: {set_text_method}")
            
    except Exception as e:
        self.log(f"ОШИБКА при настройке хуков: {str(e)}")
        # Показываем детальную информацию об ошибке
        import traceback
        self.log(f"Трассировка: {traceback.format_exc()}")
```

### 2. Мониторинг производительности

```python
import time

class PerformanceTextHook(MethodHook):
    def __init__(self, plugin_instance):
        super().__init__()
        self.plugin = plugin_instance
        self.hook_count = 0
        self.total_time = 0
    
    def before_hooked_method(self, param):
        self.start_time = time.time()
    
    def after_hooked_method(self, param):
        try:
            end_time = time.time()
            execution_time = end_time - self.start_time
            
            self.hook_count += 1
            self.total_time += execution_time
            
            # Логируем статистику каждые 100 вызовов
            if self.hook_count % 100 == 0:
                avg_time = self.total_time / self.hook_count
                self.plugin.log(f"Статистика: {self.hook_count} вызовов, среднее время: {avg_time:.4f}с")
            
            # Основная логика изменения цвета
            if self.plugin.get_setting("enabled", True):
                text_view = param.thisObject
                text_view.setTextColor(0xFFFF0000)
                
        except Exception as e:
            self.plugin.log(f"Error in PerformanceTextHook: {str(e)}")
```

## Советы по использованию

1. **Тестирование**: Всегда тестируйте плагин в безопасной среде
2. **Производительность**: Минимизируйте логику в хуках для лучшей производительности
3. **Совместимость**: Проверяйте совместимость с другими плагинами
4. **Обновления**: Следите за обновлениями Exteragram API
5. **Резервные копии**: Делайте резервные копии настроек плагина
